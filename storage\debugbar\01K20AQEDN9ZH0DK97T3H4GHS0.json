{"__meta": {"id": "01K20AQEDN9ZH0DK97T3H4GHS0", "datetime": "2025-08-07 00:06:34", "utime": **********.61426, "method": "GET", "uri": "/admin/sales/performance/stats?type=target-vs-actual&period=monthly&view_type=individual", "ip": "127.0.0.1"}, "messages": {"count": 5, "messages": [{"message": "[00:06:34] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$app is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.542821, "xdebug_link": null, "collector": "log"}, {"message": "[00:06:34] LOG.warning: Creation of dynamic property Webkul\\Admin\\Http\\Middleware\\Locale::$request is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php on line 22", "message_html": null, "is_string": false, "label": "warning", "time": **********.543063, "xdebug_link": null, "collector": "log"}, {"message": "[00:06:34] LOG.info: SalesPerformanceRepository getTargetVsActual SQL: select `entity_name`, `target_amount`, `achieved_amount`, `achievement_percentage`, `period_start`, `period_end` from `sales_performance` where `entity_type` = ? and (`period_start` between ? and ? or `period_end` between ? and ? or (`period_start` <= ? and `period_end` >= ?))", "message_html": null, "is_string": false, "label": "info", "time": **********.591211, "xdebug_link": null, "collector": "log"}, {"message": "[00:06:34] LOG.info: SalesPerformanceRepository getTargetVsActual Bindings: [\"individual\",\"2025-02-07\",\"2025-08-07\",\"2025-02-07\",\"2025-08-07\",\"2025-02-07\",\"2025-08-07\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.593145, "xdebug_link": null, "collector": "log"}, {"message": "[00:06:34] LOG.info: SalesPerformanceRepository getTargetVsActual Results: [{\"entity_name\":\"Example Admin\",\"target_amount\":\"100387.00\",\"achieved_amount\":\"84092.00\",\"achievement_percentage\":\"83.77\",\"period_start\":\"2024-12-31T18:30:00.000000Z\",\"period_end\":\"2025-03-30T18:30:00.000000Z\"},{\"entity_name\":\"Example Admin\",\"target_amount\":\"19411.00\",\"achieved_amount\":\"18447.00\",\"achievement_percentage\":\"95.03\",\"period_start\":\"2025-01-31T18:30:00.000000Z\",\"period_end\":\"2025-02-27T18:30:00.000000Z\"},{\"entity_name\":\"Example Admin\",\"target_amount\":\"2000.00\",\"achieved_amount\":\"35157.00\",\"achievement_percentage\":\"88.66\",\"period_start\":\"2025-02-27T18:30:00.000000Z\",\"period_end\":\"2025-03-29T18:30:00.000000Z\"},{\"entity_name\":\"Example Admin\",\"target_amount\":\"71166.00\",\"achieved_amount\":\"30963.00\",\"achievement_percentage\":\"43.51\",\"period_start\":\"2025-03-31T18:30:00.000000Z\",\"period_end\":\"2025-06-29T18:30:00.000000Z\"},{\"entity_name\":\"Example Admin\",\"target_amount\":\"168957.00\",\"achieved_amount\":\"82784.00\",\"achievement_percentage\":\"49.00\",\"period_start\":\"2025-06-30T18:30:00.000000Z\",\"period_end\":\"2025-09-29T18:30:00.000000Z\"},{\"entity_name\":\"Ashutosh Sharma\",\"target_amount\":\"12000.00\",\"achieved_amount\":\"0.00\",\"achievement_percentage\":\"83.33\",\"period_start\":\"2025-07-15T18:30:00.000000Z\",\"period_end\":\"2025-08-30T18:30:00.000000Z\"}]", "message_html": null, "is_string": false, "label": "info", "time": **********.606266, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.152181, "end": **********.614289, "duration": 0.46210813522338867, "duration_str": "462ms", "measures": [{"label": "Booting", "start": **********.152181, "relative_start": 0, "end": **********.485823, "relative_end": **********.485823, "duration": 0.*****************, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.485835, "relative_start": 0.*****************, "end": **********.614292, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.516635, "relative_start": 0.****************, "end": **********.525421, "relative_end": **********.525421, "duration": 0.*****************, "duration_str": "8.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.611306, "relative_start": 0.****************, "end": **********.611743, "relative_end": **********.611743, "duration": 0.00043702125549316406, "duration_str": "437μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.25", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.011009999999999999, "accumulated_duration_str": "11.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1152}, {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 557}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}], "start": **********.555765, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:1152", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1152}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=1152", "ajax": false, "filename": "BaseRepository.php", "line": "1152"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `core_config` where `code` = 'general.general.locale_settings.locale'", "type": "query", "params": [], "bindings": ["general.general.locale_settings.locale"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 38}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 178}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Core\\src\\Core.php", "line": 241}], "start": **********.56166, "duration": 0.00509, "duration_str": "5.09ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "laravel-crm", "explain": null, "start_percent": 0, "width_percent": 46.231}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.583044, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "laravel-crm", "explain": null, "start_percent": 46.231, "width_percent": 4.905}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, {"index": 22, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "admin_locale", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Locale.php", "line": 38}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.588424, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "user:56", "source": {"index": 21, "namespace": "middleware", "name": "user", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\Bouncer.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=56", "ajax": false, "filename": "Bouncer.php", "line": "56"}, "connection": "laravel-crm", "explain": null, "start_percent": 51.135, "width_percent": 3.906}, {"sql": "select `entity_name`, `target_amount`, `achieved_amount`, `achievement_percentage`, `period_start`, `period_end` from `sales_performance` where `entity_type` = 'individual' and (`period_start` between '2025-02-07' and '2025-08-07' or `period_end` between '2025-02-07' and '2025-08-07' or (`period_start` <= '2025-02-07' and `period_end` >= '2025-08-07')) order by `period_start` asc, `achievement_percentage` desc", "type": "query", "params": [], "bindings": ["individual", "2025-02-07", "2025-08-07", "2025-02-07", "2025-08-07", "2025-02-07", "2025-08-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/SalesPerformanceRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Sales\\src\\Repositories\\SalesPerformanceRepository.php", "line": 136}, {"index": 16, "namespace": null, "name": "packages/Webkul/Sales/src/Http/Controllers/PerformanceController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Sales\\src\\Http\\Controllers\\PerformanceController.php", "line": 108}, {"index": 17, "namespace": null, "name": "packages/Webkul/Sales/src/Http/Controllers/PerformanceController.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Sales\\src\\Http\\Controllers\\PerformanceController.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.593428, "duration": 0.00495, "duration_str": "4.95ms", "memory": 0, "memory_str": null, "filename": "SalesPerformanceRepository.php:136", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Sales/src/Repositories/SalesPerformanceRepository.php", "file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Sales\\src\\Repositories\\SalesPerformanceRepository.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FSales%2Fsrc%2FRepositories%2FSalesPerformanceRepository.php&line=136", "ajax": false, "filename": "SalesPerformanceRepository.php", "line": "136"}, "connection": "laravel-crm", "explain": null, "start_percent": 55.041, "width_percent": 44.959}]}, "models": {"data": {"Webkul\\Sales\\Models\\SalesPerformance": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FSalesPerformance.php&line=1", "ajax": false, "filename": "SalesPerformance.php", "line": "?"}}, "Webkul\\User\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/sales/performance/stats?period=monthly&type=target-vs-actual&view_type=i...", "action_name": "admin.sales.performance.stats", "controller_action": "Webkul\\Sales\\Http\\Controllers\\PerformanceController@stats", "uri": "GET admin/sales/performance/stats", "controller": "Webkul\\Sales\\Http\\Controllers\\PerformanceController@stats<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FSales%2Fsrc%2FHttp%2FControllers%2FPerformanceController.php&line=35\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/sales/performance", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FSales%2Fsrc%2FHttp%2FControllers%2FPerformanceController.php&line=35\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Sales/src/Http/Controllers/PerformanceController.php:35-51</a>", "middleware": "web, admin_locale, user", "duration": "464ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-371378909 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">target-vs-actual</span>\"\n  \"<span class=sf-dump-key>period</span>\" => \"<span class=sf-dump-str title=\"7 characters\">monthly</span>\"\n  \"<span class=sf-dump-key>view_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">individual</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371378909\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-542133621 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-542133621\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1817611785 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlRJVlNhbFpaQnlUL05LUm94NlpGQ2c9PSIsInZhbHVlIjoiMU8xK2h0bWRwNlBuRHdKczdZdWZ3d0JUWS8xQzkrU1RlZVV3M1JiQnZHL2taQ25wK2R1OCtRUFJ1NlN4YzJXTkVTZXB6RStRK1hjYkNGY0Z2S1V1WDFJVzNtazkrR0FyQk1uL2ZoZ1U4UlVTSTM0RnVMSERXQ2tuclhqbE44SjkiLCJtYWMiOiI5OTcxMThjNThhZGZmNDhjMjQ1NWQ2Y2VhMTk5NGYyZDM3YTFmOTRiMzU5NWJiZjYxZWRlZWU4OTQ3NzQ5NWUwIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/admin/sales/performance</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"729 characters\">dark_mode=0; XSRF-TOKEN=eyJpdiI6IlRJVlNhbFpaQnlUL05LUm94NlpGQ2c9PSIsInZhbHVlIjoiMU8xK2h0bWRwNlBuRHdKczdZdWZ3d0JUWS8xQzkrU1RlZVV3M1JiQnZHL2taQ25wK2R1OCtRUFJ1NlN4YzJXTkVTZXB6RStRK1hjYkNGY0Z2S1V1WDFJVzNtazkrR0FyQk1uL2ZoZ1U4UlVTSTM0RnVMSERXQ2tuclhqbE44SjkiLCJtYWMiOiI5OTcxMThjNThhZGZmNDhjMjQ1NWQ2Y2VhMTk5NGYyZDM3YTFmOTRiMzU5NWJiZjYxZWRlZWU4OTQ3NzQ5NWUwIiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6IkgzSUtQQi95V29SVnh6WisrSkt5M0E9PSIsInZhbHVlIjoiL20wQ0E4ZW8wZ2VZOHUvVjhRTWpPMWwyUTVzcnZHV3EycUFZMGxFZHdtM2hud3k5MlE3Y3NGMWpvdzRDQ3lkQkVwMEwxUE9GQTJ5TDZJU28rRnFhSkk4cmFlWm9kcnJ0MWJhd0lpcU1FNkg2TGZUTkVnazBmbnVPOEFXMUlZbmciLCJtYWMiOiI2YzUwNTg1NDM5ZmU4ZmQ1ODRmMmQ2MWY1ZmRlYmQxZTRiMjE2ZDYwYzk1NmRjNTFiODlmMDkyZDQ4YjRhMTc5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817611785\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-30228722 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YR6ZkDT7UE4jyE2fbcue3BuwR3L7TeSvx8kOAuYl</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JH8hV8RhH1ybxilg2ftN8UlDwm0XOnWHvsSt7Jss</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-30228722\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-224968591 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 06 Aug 2025 18:36:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224968591\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1851463242 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YR6ZkDT7UE4jyE2fbcue3BuwR3L7TeSvx8kOAuYl</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/admin/sales/performance</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1851463242\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/sales/performance/stats?period=monthly&type=target-vs-actual&view_type=i...", "action_name": "admin.sales.performance.stats", "controller_action": "Webkul\\Sales\\Http\\Controllers\\PerformanceController@stats"}, "badge": null}}