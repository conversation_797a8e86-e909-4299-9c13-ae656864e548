<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales_performance', function (Blueprint $table) {
            $table->unsignedBigInteger('sales_target_id')->nullable()->after('id');
            $table->foreign('sales_target_id')->references('id')->on('sales_targets')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales_performance', function (Blueprint $table) {
            $table->dropForeign(['sales_target_id']);
            $table->dropColumn('sales_target_id');
        });
    }
};
