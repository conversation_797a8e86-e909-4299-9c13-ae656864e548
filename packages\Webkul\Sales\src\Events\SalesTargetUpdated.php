<?php

namespace Webkul\Sales\Events;

use Illuminate\Queue\SerializesModels;
use Webkul\Sales\Models\SalesTarget;

class SalesTargetUpdated
{
    use SerializesModels;

    /**
     * The sales target instance.
     *
     * @var \Webkul\Sales\Models\SalesTarget
     */
    public $salesTarget;

    /**
     * Create a new event instance.
     *
     * @param  \Webkul\Sales\Models\SalesTarget  $salesTarget
     * @return void
     */
    public function __construct(SalesTarget $salesTarget)
    {
        $this->salesTarget = $salesTarget;
    }
}
