<?php

namespace Webkul\Sales\Repositories;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Webkul\Core\Eloquent\Repository;
use Webkul\Sales\Models\SalesReport;

class SalesReportRepository extends Repository
{
    /**
     * Specify Model class name
     */
    public function model(): string
    {
        return SalesReport::class;
    }

    /**
     * Get data grid data for reports.
     */
    public function getDataGridData(): JsonResponse
    {
        $query = $this->model->query()
            ->with(['creator:id,name'])
            ->accessibleBy(auth()->id())
            ->select([
                'id',
                'name',
                'type',
                'status',
                'date_from',
                'date_to',
                'generated_at',
                'created_by',
                'is_public',
                'is_scheduled',
                'created_at',
                'updated_at'
            ]);

        // Apply filters
        if (request('search')) {
            $search = request('search');
            $query->where('name', 'like', "%{$search}%");
        }

        if (request('type')) {
            $query->where('type', request('type'));
        }

        if (request('status')) {
            $query->where('status', request('status'));
        }

        if (request('date_from')) {
            $query->where('date_from', '>=', request('date_from'));
        }

        if (request('date_to')) {
            $query->where('date_to', '<=', request('date_to'));
        }

        // Apply sorting
        $sortBy = request('sort', 'created_at');
        $sortOrder = request('order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = request('per_page', 15);
        $reports = $query->paginate($perPage);

        return new JsonResponse([
            'data' => $reports->items(),
            'meta' => [
                'current_page' => $reports->currentPage(),
                'last_page'    => $reports->lastPage(),
                'per_page'     => $reports->perPage(),
                'total'        => $reports->total(),
            ],
        ]);
    }

    /**
     * Generate report data.
     */
    public function generateReport(int $reportId): void
    {
        $report = $this->find($reportId);

        if (!$report) {
            return;
        }

        try {
            $report->update(['status' => 'processing']);

            $data = $this->buildReportData($report);

            $report->update([
                'status' => 'completed',
                'data' => json_encode($data),
                'generated_at' => now(),
                'error_message' => null,
            ]);

            // Generate file if needed
            $this->generateReportFile($report, $data);

        } catch (\Exception $e) {
            $report->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Build report data based on type and filters.
     */
    protected function buildReportData(SalesReport $report): array
    {
        switch ($report->type) {
            case 'commission':
                return $this->buildCommissionReport($report);
            case 'yoy_growth':
                return $this->buildYoyGrowthReport($report);
            case 'pipeline_health':
                return $this->buildPipelineHealthReport($report);
            case 'custom':
                return $this->buildCustomReport($report);
            default:
                throw new \Exception('Unknown report type: ' . $report->type);
        }
    }

    /**
     * Build commission report data.
     */
    protected function buildCommissionReport(SalesReport $report): array
    {
        $query = DB::table('sales_performance as sp')
            ->join('users as u', 'sp.entity_id', '=', 'u.id')
            ->where('sp.entity_type', 'individual')
            ->whereBetween('sp.period_start', [$report->date_from, $report->date_to])
            ->select(
                'u.name as sales_rep',
                DB::raw('SUM(sp.achieved_amount) as total_sales'),
                DB::raw('AVG(u.commission_rate) as commission_rate'), // Assuming commission_rate is on users table
                DB::raw('SUM(sp.achieved_amount) * AVG(u.commission_rate) / 100 as commission_amount')
            )
            ->groupBy('u.id', 'u.name');

        $results = $query->get();

        $totalSales = $results->sum('total_sales');
        $totalCommission = $results->sum('commission_amount');

        return [
            'headers' => ['Sales Rep', 'Total Sales', 'Commission Rate', 'Commission Amount'],
            'rows'    => $results->map(function($item) {
                return [
                    $item->sales_rep,
                    '$' . number_format($item->total_sales, 2),
                    number_format($item->commission_rate, 2) . '%',
                    '$' . number_format($item->commission_amount, 2),
                ];
            })->toArray(),
            'summary' => [
                'total_sales' => $totalSales,
                'total_commission' => $totalCommission,
            ],
        ];
    }

    /**
     * Build year-over-year growth report data.
     */
    protected function buildYoyGrowthReport(SalesReport $report): array
    {
        $currentYearQuery = DB::table('sales_performance')
            ->whereBetween('period_start', [$report->date_from, $report->date_to])
            ->select(DB::raw('SUM(achieved_amount) as total'), DB::raw('DATE_FORMAT(period_start, \'%Y-%m%\') as period'))
            ->groupBy('period');

        $previousYearQuery = DB::table('sales_performance')
            ->whereBetween('period_start', [date('Y-m-d', strtotime('-1 year', strtotime($report->date_from))), date('Y-m-d', strtotime('-1 year', strtotime($report->date_to)))])
            ->select(DB::raw('SUM(achieved_amount) as total'), DB::raw('DATE_FORMAT(period_start, \'%Y-%m%\') as period'))
            ->groupBy('period');

        $currentYearData = $currentYearQuery->get()->keyBy('period');
        $previousYearData = $previousYearQuery->get()->keyBy('period');

        $periods = $currentYearData->keys()->merge($previousYearData->keys())->unique()->sort();

        $rows = $periods->map(function($period) use ($currentYearData, $previousYearData) {
            $current = $currentYearData->get($period) ? $currentYearData->get($period)->total : 0;
            $previousPeriod = date('Y-m', strtotime('-1 year', strtotime($period . '-01')));
            $previous = $previousYearData->get($previousPeriod) ? $previousYearData->get($previousPeriod)->total : 0;
            $growth = $previous > 0 ? (($current - $previous) / $previous) * 100 : ($current > 0 ? 100 : 0);

            return [
                $period,
                '$' . number_format($current, 2),
                '$' . number_format($previous, 2),
                number_format($growth, 2) . '%',
            ];
        });

        return [
            'headers' => ['Period', 'Current Year', 'Previous Year', 'Growth %'],
            'rows'    => $rows->toArray(),
            'summary' => [
                'total_current' => $currentYearData->sum('total'),
                'total_previous' => $previousYearData->sum('total'),
                'overall_growth' => $previousYearData->sum('total') > 0 ? (($currentYearData->sum('total') - $previousYearData->sum('total')) / $previousYearData->sum('total')) * 100 : ($currentYearData->sum('total') > 0 ? 100 : 0),
            ],
        ];
    }

    /**
     * Build pipeline health report data.
     */
    protected function buildPipelineHealthReport(SalesReport $report): array
    {
        $query = DB::table('leads as l')
            ->join('lead_pipeline_stages as s', 'l.lead_pipeline_stage_id', '=', 's.id')
            ->whereBetween('l.created_at', [$report->date_from, $report->date_to])
            ->select(
                's.name as stage',
                DB::raw('COUNT(l.id) as count'),
                DB::raw('SUM(l.lead_value) as value'),
                DB::raw('AVG(DATEDIFF(l.updated_at, l.created_at)) as avg_days'),
                DB::raw('(SUM(CASE WHEN s.code = \'won\' THEN 1 ELSE 0 END) / COUNT(l.id)) * 100 as conversion_rate')
            );

        $results = $query->get();

        $totalPipelineValue = $results->sum('value');
        $totalLeads = $results->sum('count');
        $avgConversionRate = $results->avg('conversion_rate');

        return [
            'headers' => ['Stage', 'Count', 'Value', 'Avg. Days', 'Conversion Rate'],
            'rows'    => $results->map(function($item) {
                return [
                    $item->stage,
                    $item->count,
                    '

    /**
     * Build custom report data.
     */
    protected function buildCustomReport(SalesReport $report): array
    {
        $columns = $report->columns;
        $filters = $report->filters ?? [];

        $query = DB::table('sales_performance as sp')
            ->leftJoin('sales_targets as st', 'sp.sales_target_id', '=', 'st.id')
            ->leftJoin('users as u', 'sp.entity_id', '=', 'u.id')
            ->whereBetween('sp.period_start', [$report->date_from, $report->date_to]);

        $selectColumns = [];
        foreach ($columns as $column) {
            if (strpos($column, '.') === false) {
                $selectColumns[] = "sp.{$column} as {$column}";
            } else {
                $selectColumns[] = "{$column} as {" . str_replace('.', '_', $column) . "}";
            }
        }
        $query->select($selectColumns);

        foreach ($filters as $filter) {
            $query->where($filter['column'], $filter['operator'], $filter['value']);
        }

        $results = $query->get()->toArray();

        return [
            'headers' => array_values($columns),
            'rows'    => array_map(function($item) {
                return array_values((array)$item);
            }, $results),
            'summary' => [],
        ];
    }

    /**
     * Generate report file (CSV/Excel).
     */
    protected function generateReportFile(SalesReport $report, array $data): void
    {
        $filename = 'reports/' . $report->id . '_' . time() . '.csv';
        $content = $this->arrayToCsv($data);

        Storage::put($filename, $content);

        $report->update(['file_path' => $filename]);
    }

    /**
     * Convert array data to CSV format.
     */
    protected function arrayToCsv(array $data): string
    {
        $output = fopen('php://temp', 'r+');

        // Write headers
        if (isset($data['headers'])) {
            fputcsv($output, $data['headers']);
        }

        // Write rows
        if (isset($data['rows'])) {
            foreach ($data['rows'] as $row) {
                fputcsv($output, $row);
            }
        }

        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);

        return $csv;
    }

    /**
     * Export report file.
     */
    public function exportReport(SalesReport $report): Response
    {
        if (!$report->file_path || !Storage::exists($report->file_path)) {
            abort(404, 'Report file not found');
        }

        $filename = $report->name . '_' . $report->generated_at->format('Y-m-d') . '.csv';

        return Storage::download($report->file_path, $filename);
    }
} . number_format($item->value, 2),
                    number_format($item->avg_days, 2),
                    number_format($item->conversion_rate, 2) . '%',
                ];
            })->toArray(),
            'summary' => [
                'total_pipeline_value' => $totalPipelineValue,
                'total_leads' => $totalLeads,
                'avg_conversion_rate' => $avgConversionRate,
            ],
        ];
    }

    /**
     * Build custom report data.
     */
    protected function buildCustomReport(SalesReport $report): array
    {
        $columns = $report->columns;
        $filters = $report->filters ?? [];

        $query = DB::table('sales_performance as sp')
            ->leftJoin('sales_targets as st', 'sp.sales_target_id', '=', 'st.id')
            ->leftJoin('users as u', 'sp.entity_id', '=', 'u.id')
            ->whereBetween('sp.period_start', [$report->date_from, $report->date_to]);

        $selectColumns = [];
        foreach ($columns as $column) {
            if (strpos($column, '.') === false) {
                $selectColumns[] = "sp.{$column} as {$column}";
            } else {
                $selectColumns[] = "{$column} as {" . str_replace('.', '_', $column) . "}";
            }
        }
        $query->select($selectColumns);

        foreach ($filters as $filter) {
            $query->where($filter['column'], $filter['operator'], $filter['value']);
        }

        $results = $query->get()->toArray();

        return [
            'headers' => array_values($columns),
            'rows'    => array_map(function($item) {
                return array_values((array)$item);
            }, $results),
            'summary' => [],
        ];
    }

    /**
     * Generate report file (CSV/Excel).
     */
    protected function generateReportFile(SalesReport $report, array $data): void
    {
        $filename = 'reports/' . $report->id . '_' . time() . '.csv';
        $content = $this->arrayToCsv($data);

        Storage::put($filename, $content);

        $report->update(['file_path' => $filename]);
    }

    /**
     * Convert array data to CSV format.
     */
    protected function arrayToCsv(array $data): string
    {
        $output = fopen('php://temp', 'r+');

        // Write headers
        if (isset($data['headers'])) {
            fputcsv($output, $data['headers']);
        }

        // Write rows
        if (isset($data['rows'])) {
            foreach ($data['rows'] as $row) {
                fputcsv($output, $row);
            }
        }

        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);

        return $csv;
    }

    /**
     * Export report file.
     */
    public function exportReport(SalesReport $report): Response
    {
        if (!$report->file_path || !Storage::exists($report->file_path)) {
            abort(404, 'Report file not found');
        }

        $filename = $report->name . '_' . $report->generated_at->format('Y-m-d') . '.csv';

        return Storage::download($report->file_path, $filename);
    }
}