<?php

namespace App\Observers;

use Webkul\Sales\Models\SalesTarget;
use Webkul\Sales\Models\SalesPerformance;

class SalesTargetObserver
{
    /**
     * Handle the SalesTarget "saved" event.
     *
     * @param  \Webkul\Sales\Models\SalesTarget  $salesTarget
     * @return void
     */
    public function saved(SalesTarget $salesTarget)
    {
        SalesPerformance::updateOrCreate(
            [
                'sales_target_id' => $salesTarget->id,
            ],
            [
                'entity_type'   => $salesTarget->assignee_type,
                'entity_id'     => $salesTarget->assignee_id,
                'period_start'  => $salesTarget->start_date,
                'period_end'    => $salesTarget->end_date,
                'period_type'   => $salesTarget->period_type,
                'target_amount' => $salesTarget->target_amount,
                'achieved_amount' => $salesTarget->achieved_amount,
                'achievement_percentage' => $salesTarget->progress_percentage,
                'calculated_at' => now(),
            ]
        );
    }
}