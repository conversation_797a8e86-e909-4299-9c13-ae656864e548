<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Webkul\Sales\Models\SalesTeam;

class PopulateSalesPerformance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sales:populate-performance';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate the sales_performance table with historical data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Populating sales performance data...');

        $targets = DB::table('sales_targets')->get();

        foreach ($targets as $target) {
            $wonLeads = 0;
            $totalLeads = 0;

            if ($target->assignee_type === 'individual') {
                $wonLeads = $this->getLeadCount($target->assignee_id, $target->start_date, $target->end_date, true);
                $totalLeads = $this->getLeadCount($target->assignee_id, $target->start_date, $target->end_date, false);
            } elseif ($target->assignee_type === 'team') {
                $team = SalesTeam::find($target->assignee_id);
                if ($team) {
                    foreach ($team->members as $member) {
                        $wonLeads += $this->getLeadCount($member->id, $target->start_date, $target->end_date, true);
                        $totalLeads += $this->getLeadCount($member->id, $target->start_date, $target->end_date, false);
                    }
                }
            }

            DB::table('sales_performance')->updateOrInsert(
                [
                    'period_start'  => $target->start_date,
                    'period_end'    => $target->end_date,
                    'period_type'   => $target->period_type,
                    'entity_id'     => $target->assignee_id,
                    'entity_type'   => $target->assignee_type,
                ],
                [
                    'entity_name'            => $target->assignee_name,
                    'target_amount'          => $target->target_amount,
                    'achieved_amount'        => $target->achieved_amount,
                    'achievement_percentage' => $target->progress_percentage,
                    'leads_count'            => $totalLeads,
                    'won_leads_count'        => $wonLeads,
                    'conversion_rate'        => $totalLeads > 0 ? ($wonLeads / $totalLeads) * 100 : 0,
                    'created_at'             => now(),
                    'updated_at'             => now(),
                ]
            );
        }

        $this->info('Sales performance data populated successfully.');
    }

    private function getLeadCount($userId, $startDate, $endDate, $won = false)
    {
        $query = DB::table('leads')
            ->where('user_id', $userId)
            ->whereBetween('created_at', [$startDate, $endDate]);

        if ($won) {
            $query->whereIn('lead_pipeline_stage_id', function ($query) {
                $query->select('id')
                    ->from('lead_pipeline_stages')
                    ->where('code', 'won');
            });
        }

        return $query->count();
    }
}