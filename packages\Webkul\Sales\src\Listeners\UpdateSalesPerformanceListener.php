<?php

namespace Webkul\Sales\Listeners;

use Webkul\Sales\Events\SalesTargetUpdated;
use Webkul\Sales\Models\SalesPerformance;

class UpdateSalesPerformanceListener
{
    /**
     * Handle the event.
     *
     * @param  \Webkul\Sales\Events\SalesTargetUpdated  $event
     * @return void
     */
    public function handle(SalesTargetUpdated $event)
    {
        $salesTarget = $event->salesTarget;

        SalesPerformance::updateOrCreate([
            'entity_type'   => $salesTarget->assignee_type,
            'entity_id'     => $salesTarget->assignee_id,
            'period_start'  => $salesTarget->start_date,
            'period_end'    => $salesTarget->end_date,
            'period_type'   => $salesTarget->period_type,
        ], [
            'target_amount' => $salesTarget->target_amount,
            'achieved_amount' => $salesTarget->achieved_amount,
            'achievement_percentage' => $salesTarget->progress_percentage,
            'calculated_at' => now(),
        ]);
    }
}
